import { Component, signal, TemplateRef, ViewChild, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule, CurrencyPipe } from '@angular/common';
import { Observable, of } from 'rxjs';
import { FormsModule } from '@angular/forms';
import { ListLayoutComponent } from '@/shared/components/list-layout/list-layout.component';
import { ListConfig, Product, SortState } from '@/shared/models/view/list-layout.model';
import { PageContextBarComponent } from '@/shared/components/page-context-bar/page-context-bar.component';

@Component({
  selector: 'app-product-list',
  standalone: true,
  imports: [
    CommonModule,
    ListLayoutComponent,
    FormsModule,
  ],
  templateUrl: './product-list.component.html',
  styleUrls: ['./product-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductListComponent {
  @ViewChild('priceRangeTemplate') priceRangeTemplate!: TemplateRef<any>;
  @ViewChild('actionsTemplate') actionsTemplate!: TemplateRef<any>;

  products$!: Observable<Product[]>;
  config = signal<ListConfig>({
    pageName: 'LIST_LAYOUT.PAGE_NAME.ALL_PRODUCTS',
    pageShortDescription: 'LIST_LAYOUT.PAGE_SHORT_DESC.ALL_PRODUCTS',
    allColumns: [
      { key: 'id', label: 'Mã SP', sortable: true },
      { key: 'name', label: 'Tên sản phẩm', sortable: true },
      { key: 'price', label: 'Giá', sortable: true },
      { key: 'stock', label: 'Tồn kho', sortable: false },
      { key: 'category', label: 'Danh mục', sortable: false },
      { key: 'brand', label: 'Thương hiệu', sortable: true },
      { key: 'sku', label: 'SKU', sortable: true },
      { key: 'weight', label: 'Trọng lượng (kg)', sortable: false },
      { key: 'dimensions', label: 'Kích thước (cm)', sortable: false },
      { key: 'color', label: 'Màu sắc', sortable: false },
      { key: 'material', label: 'Chất liệu', sortable: false },
      { key: 'origin', label: 'Xuất xứ', sortable: false },
      { key: 'warranty', label: 'Bảo hành (tháng)', sortable: true },
      { key: 'releaseDate', label: 'Ngày phát hành', sortable: true },
      { key: 'discount', label: 'Giảm giá (%)', sortable: true },
      { key: 'rating', label: 'Đánh giá', sortable: true },
      { key: 'sales', label: 'Doanh số', sortable: true },
      { key: 'status', label: 'Trạng thái', sortable: false },
      { key: 'supplier', label: 'Nhà cung cấp', sortable: false },
      { key: 'description', label: 'Mô tả', sortable: false }
    ],
    columns: [
      { key: 'name', label: 'Tên sản phẩm', sortable: true },
      { key: 'price', label: 'Giá', sortable: true },
      { key: 'stock', label: 'Tồn kho', sortable: false },
      { key: 'category', label: 'Danh mục', sortable: false },
      { key: 'brand', label: 'Thương hiệu', sortable: true },
      { key: 'sku', label: 'SKU', sortable: true },
      { key: 'weight', label: 'Trọng lượng (kg)', sortable: false },
      { key: 'color', label: 'Màu sắc', sortable: false },
      { key: 'warranty', label: 'Bảo hành (tháng)', sortable: true },
      { key: 'rating', label: 'Đánh giá', sortable: true }
    ],
    fixedColumns: [
      { key: 'id', label: 'Mã SP', sortable: true },
      { key: 'name', label: 'Tên sản phẩm', sortable: true },
      { key: 'price', label: 'Giá', sortable: true }
    ],
    filters: [
      { key: 'search', label: 'Tìm kiếm', type: 'search' },
      {
        key: 'category',
        label: 'Danh mục',
        type: 'select',
        options: [
          { value: 'fashion', label: 'Thời trang' },
          { value: 'electronics', label: 'Điện tử' },
          { value: 'beauty', label: 'Mỹ phẩm' },
          { value: 'home', label: 'Gia dụng' }
        ]
      },
      {
        key: 'brand',
        label: 'Thương hiệu',
        type: 'select',
        options: [
          { value: 'nike', label: 'Nike' },
          { value: 'samsung', label: 'Samsung' },
          { value: 'loreal', label: "L'Oréal" },
          { value: 'ikea', label: 'IKEA' }
        ]
      }
    ],
    items: [],
    columnOrder: [
      'stock',
      'category',
      'brand',
      'sku',
      'weight',
      'color',
      'warranty',
      'rating'
    ],
    pager: {
      currentPage: 1,
      totalPages: 1, // Sẽ tính lại trong fetchProducts
      pageSize: 20
    },
  });
  filterValues = signal<Record<string, any>>({});
  sortState = signal<SortState>({ key: '', direction: '' });
  currentPage = signal<number>(1);
  pageSize = signal<number>(50);
  totalItems = signal<number>(50); // Tổng số sản phẩm

  constructor() {}

  ngAfterViewInit() {
    this.config.set({
      ...this.config(),
      filters: [
        ...this.config().filters,
        { key: 'priceRange', label: 'Khoảng giá', type: 'custom', template: this.priceRangeTemplate }
      ]
    });
  }

  ngOnInit() {
    this.fetchProducts();
  }

  fetchProducts() {
    const page = this.currentPage();
    const pageSize = this.pageSize();
    const mockProducts: Product[] = Array.from({ length: 50 }, (_, index) => ({
      id: `${index + 1}`,
      name: `Sản phẩm ${index + 1}`,
      price: Math.round(Math.random() * 500 + 50),
      stock: Math.round(Math.random() * 100),
      category: ['fashion', 'electronics', 'beauty', 'home'][Math.floor(Math.random() * 4)],
      brand: ['Nike', 'Samsung', "L'Oréal", 'IKEA'][Math.floor(Math.random() * 4)],
      sku: `SKU-${index + 1}-${Math.random().toString(36).slice(2, 7)}`,
      weight: Number((Math.random() * 5 + 0.1).toFixed(2)),
      dimensions: `${Math.round(Math.random() * 50)}x${Math.round(Math.random() * 50)}x${Math.round(Math.random() * 50)}`,
      color: ['Đỏ', 'Xanh', 'Đen', 'Trắng'][Math.floor(Math.random() * 4)],
      material: ['Vải', 'Nhựa', 'Kim loại', 'Gỗ'][Math.floor(Math.random() * 4)],
      origin: ['Việt Nam', 'Trung Quốc', 'Mỹ', 'Nhật Bản'][Math.floor(Math.random() * 4)],
      warranty: Math.round(Math.random() * 24),
      releaseDate: `202${Math.floor(Math.random() * 5)}-0${Math.floor(Math.random() * 9) + 1}-01`,
      discount: Math.round(Math.random() * 50),
      rating: Number((Math.random() * 5).toFixed(1)),
      sales: Math.round(Math.random() * 1000),
      status: ['Còn hàng', 'Hết hàng', 'Sắp có'][Math.floor(Math.random() * 3)],
      supplier: ['Công ty A', 'Công ty B', 'Công ty C'][Math.floor(Math.random() * 3)],
      description: `Mô tả sản phẩm ${index + 1}`
    }));

    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const paginatedProducts = mockProducts.slice(start, end);

    this.products$ = of(paginatedProducts);
    this.products$.subscribe(products => {
      const totalPages = Math.ceil(this.totalItems() / pageSize);
      this.config.set({
        ...this.config(),
        items: products,
        pager: {
          currentPage: page,
          totalPages,
          pageSize
        }
      });
    });
  }

  onFilterChange(filters: Record<string, any>) {
    this.filterValues.set(filters);
    this.currentPage.set(1);
    this.fetchProducts();
  }

  onSortChange(sort: SortState) {
    this.sortState.set(sort);
    this.currentPage.set(1);
    this.fetchProducts();
  }

  onColumnsChange({ visibleColumns, columnOrder }: { visibleColumns: string[]; columnOrder: string[] }) {
    this.config.set({
      ...this.config(),
      columns: this.config().allColumns.filter(col => visibleColumns.includes(col.key)),
      columnOrder
    });
    this.currentPage.set(1);
    this.fetchProducts();
  }

  onPageChange(event: { page: number; pageSize: number }) {
    this.currentPage.set(event.page);
    this.pageSize.set(event.pageSize);
    this.fetchProducts();
  }

  handleActionClick(data: { item: Product; actionType: string; extra?: any }) {
    console.log('Action:', data.actionType, 'Product:', data.item, 'Extra:', data.extra);
    switch (data.actionType) {
      case 'view':
        console.log(`Viewing details for ${data.item.name}`);
        break;
      case 'edit':
        console.log(`Editing ${data.item.name} with extra:`, data.extra);
        break;
      case 'delete':
        console.log(`Deleting ${data.item.name}`);
        break;
    }
  }

  hehe() {
    console.log('ok')
  }
}
