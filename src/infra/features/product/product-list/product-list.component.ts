import { Component, signal, TemplateRef, ViewChild, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule, CurrencyPipe } from '@angular/common';
import { Observable, of } from 'rxjs';
import { FormsModule } from '@angular/forms';
import { ListLayoutComponent } from '@/shared/components/list-layout/list-layout.component';
import { ListConfig, Product, SortState, ActionItem } from '@/shared/models/view/list-layout.model';
import { PageContextBarComponent } from '@/shared/components/page-context-bar/page-context-bar.component';

@Component({
  selector: 'app-product-list',
  standalone: true,
  imports: [
    CommonModule,
    ListLayoutComponent,
    FormsModule,
  ],
  templateUrl: './product-list.component.html',
  styleUrls: ['./product-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductListComponent {
  @ViewChild('priceRangeTemplate') priceRangeTemplate!: TemplateRef<any>;
  @ViewChild('actionsTemplate') actionsTemplate!: TemplateRef<any>;

  products$!: Observable<Product[]>;
  config = signal<ListConfig>({
    pageName: 'LIST_LAYOUT.PAGE_NAME.ALL_PRODUCTS',
    pageShortDescription: 'LIST_LAYOUT.PAGE_SHORT_DESC.ALL_PRODUCTS',
    allColumns: [
      { key: 'id', label: 'Mã SP', sortable: true },
      { key: 'name', label: 'Tên sản phẩm', sortable: true },
      { key: 'price', label: 'Giá', sortable: true },
      { key: 'stock', label: 'Tồn kho', sortable: false },
      { key: 'category', label: 'Danh mục', sortable: false },
      { key: 'brand', label: 'Thương hiệu', sortable: true },
      { key: 'sku', label: 'SKU', sortable: true },
      { key: 'weight', label: 'Trọng lượng (kg)', sortable: false },
      { key: 'dimensions', label: 'Kích thước (cm)', sortable: false },
      { key: 'color', label: 'Màu sắc', sortable: false },
      { key: 'material', label: 'Chất liệu', sortable: false },
      { key: 'origin', label: 'Xuất xứ', sortable: false },
      { key: 'warranty', label: 'Bảo hành (tháng)', sortable: true },
      { key: 'releaseDate', label: 'Ngày phát hành', sortable: true },
      { key: 'discount', label: 'Giảm giá (%)', sortable: true },
      { key: 'rating', label: 'Đánh giá', sortable: true },
      { key: 'sales', label: 'Doanh số', sortable: true },
      { key: 'status', label: 'Trạng thái', sortable: false },
      { key: 'supplier', label: 'Nhà cung cấp', sortable: false },
      { key: 'description', label: 'Mô tả', sortable: false }
    ],
    columns: [
      { key: 'name', label: 'Tên sản phẩm', sortable: true },
      { key: 'price', label: 'Giá', sortable: true },
      { key: 'stock', label: 'Tồn kho', sortable: false },
      { key: 'category', label: 'Danh mục', sortable: false },
      { key: 'brand', label: 'Thương hiệu', sortable: true },
      { key: 'sku', label: 'SKU', sortable: true },
      { key: 'weight', label: 'Trọng lượng (kg)', sortable: false },
      { key: 'color', label: 'Màu sắc', sortable: false },
      { key: 'warranty', label: 'Bảo hành (tháng)', sortable: true },
      { key: 'rating', label: 'Đánh giá', sortable: true }
    ],
    fixedColumns: [
      { key: 'id', label: 'Mã SP', sortable: true },
      { key: 'name', label: 'Tên sản phẩm', sortable: true },
      { key: 'price', label: 'Giá', sortable: true }
    ],
    filters: [
      { key: 'search', label: 'Tìm kiếm', type: 'search' },
      {
        key: 'category',
        label: 'Danh mục',
        type: 'select',
        options: [
          { value: 'fashion', label: 'Thời trang' },
          { value: 'electronics', label: 'Điện tử' },
          { value: 'beauty', label: 'Mỹ phẩm' },
          { value: 'home', label: 'Gia dụng' }
        ]
      },
      {
        key: 'brand',
        label: 'Thương hiệu',
        type: 'select',
        options: [
          { value: 'nike', label: 'Nike' },
          { value: 'samsung', label: 'Samsung' },
          { value: 'loreal', label: "L'Oréal" },
          { value: 'ikea', label: 'IKEA' }
        ]
      }
    ],
    items: [],
    columnOrder: [
      'stock',
      'category',
      'brand',
      'sku',
      'weight',
      'color',
      'warranty',
      'rating'
    ],
    pager: {
      currentPage: 1,
      totalPages: 1, // Sẽ tính lại trong fetchProducts
      pageSize: 20
    },

    // Enable selection and row actions
    enableSelection: true,
    enableRowActions: true,

    // Default actions (hiển thị khi không có item nào được chọn)
    defaultActions: [
      {
        key: 'add-product',
        label: 'LIST_LAYOUT.ACTIONS.ADD_PRODUCT',
        icon: 'fa-light fa-plus',
        actionType: 'add-product',
        tooltip: 'Thêm sản phẩm mới'
      },
      {
        key: 'import',
        label: 'LIST_LAYOUT.ACTIONS.IMPORT',
        icon: 'fa-light fa-upload',
        actionType: 'import',
        tooltip: 'Nhập dữ liệu từ file'
      }
    ],

    // Selected actions (hiển thị khi có items được chọn)
    selectedActions: [
      {
        key: 'bulk-delete',
        label: 'LIST_LAYOUT.ACTIONS.BULK_DELETE',
        icon: 'fa-light fa-trash',
        actionType: 'bulk-delete',
        tooltip: 'Xóa các sản phẩm đã chọn'
      },
      {
        key: 'bulk-edit',
        label: 'LIST_LAYOUT.ACTIONS.BULK_EDIT',
        icon: 'fa-light fa-edit',
        actionType: 'bulk-edit',
        tooltip: 'Chỉnh sửa hàng loạt'
      },
      {
        key: 'bulk-export',
        label: 'LIST_LAYOUT.ACTIONS.BULK_EXPORT',
        icon: 'fa-light fa-download',
        actionType: 'bulk-export',
        tooltip: 'Xuất dữ liệu đã chọn'
      }
    ],

    // Row actions (hiển thị trong menu của từng dòng)
    rowActions: [
      {
        key: 'edit',
        label: 'LIST_LAYOUT.ACTIONS.EDIT',
        icon: 'edit',
        actionType: 'edit',
        tooltip: 'Chỉnh sửa sản phẩm'
      },
      {
        key: 'view-details',
        label: 'LIST_LAYOUT.ACTIONS.VIEW_DETAILS',
        icon: 'visibility',
        actionType: 'view-details',
        tooltip: 'Xem chi tiết sản phẩm'
      },
      {
        key: 'duplicate',
        label: 'LIST_LAYOUT.ACTIONS.DUPLICATE',
        icon: 'content_copy',
        actionType: 'duplicate',
        tooltip: 'Nhân bản sản phẩm'
      },
      {
        key: 'delete',
        label: 'LIST_LAYOUT.ACTIONS.DELETE',
        icon: 'delete',
        actionType: 'delete',
        tooltip: 'Xóa sản phẩm'
      }
    ]
  });
  filterValues = signal<Record<string, any>>({});
  sortState = signal<SortState>({ key: '', direction: '' });
  currentPage = signal<number>(1);
  pageSize = signal<number>(50);
  totalItems = signal<number>(50); // Tổng số sản phẩm
  selectedItems = signal<string[]>([]); // Track selected product IDs

  constructor() {}

  ngAfterViewInit() {
    this.config.set({
      ...this.config(),
      filters: [
        ...this.config().filters,
        { key: 'priceRange', label: 'Khoảng giá', type: 'custom', template: this.priceRangeTemplate }
      ]
    });
  }

  ngOnInit() {
    this.fetchProducts();
  }

  fetchProducts() {
    const page = this.currentPage();
    const pageSize = this.pageSize();
    const mockProducts: Product[] = Array.from({ length: 50 }, (_, index) => ({
      id: `${index + 1}`,
      name: `Sản phẩm ${index + 1}`,
      price: Math.round(Math.random() * 500 + 50),
      stock: Math.round(Math.random() * 100),
      category: ['fashion', 'electronics', 'beauty', 'home'][Math.floor(Math.random() * 4)],
      brand: ['Nike', 'Samsung', "L'Oréal", 'IKEA'][Math.floor(Math.random() * 4)],
      sku: `SKU-${index + 1}-${Math.random().toString(36).slice(2, 7)}`,
      weight: Number((Math.random() * 5 + 0.1).toFixed(2)),
      dimensions: `${Math.round(Math.random() * 50)}x${Math.round(Math.random() * 50)}x${Math.round(Math.random() * 50)}`,
      color: ['Đỏ', 'Xanh', 'Đen', 'Trắng'][Math.floor(Math.random() * 4)],
      material: ['Vải', 'Nhựa', 'Kim loại', 'Gỗ'][Math.floor(Math.random() * 4)],
      origin: ['Việt Nam', 'Trung Quốc', 'Mỹ', 'Nhật Bản'][Math.floor(Math.random() * 4)],
      warranty: Math.round(Math.random() * 24),
      releaseDate: `202${Math.floor(Math.random() * 5)}-0${Math.floor(Math.random() * 9) + 1}-01`,
      discount: Math.round(Math.random() * 50),
      rating: Number((Math.random() * 5).toFixed(1)),
      sales: Math.round(Math.random() * 1000),
      status: ['Còn hàng', 'Hết hàng', 'Sắp có'][Math.floor(Math.random() * 3)],
      supplier: ['Công ty A', 'Công ty B', 'Công ty C'][Math.floor(Math.random() * 3)],
      description: `Mô tả sản phẩm ${index + 1}`
    }));

    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const paginatedProducts = mockProducts.slice(start, end);

    this.products$ = of(paginatedProducts);
    this.products$.subscribe(products => {
      const totalPages = Math.ceil(this.totalItems() / pageSize);
      this.config.set({
        ...this.config(),
        items: products,
        pager: {
          currentPage: page,
          totalPages,
          pageSize
        }
      });
    });
  }

  onFilterChange(filters: Record<string, any>) {
    this.filterValues.set(filters);
    this.currentPage.set(1);
    this.fetchProducts();
  }

  onSortChange(sort: SortState) {
    this.sortState.set(sort);
    this.currentPage.set(1);
    this.fetchProducts();
  }

  onColumnsChange({ visibleColumns, columnOrder }: { visibleColumns: string[]; columnOrder: string[] }) {
    this.config.set({
      ...this.config(),
      columns: this.config().allColumns.filter(col => visibleColumns.includes(col.key)),
      columnOrder
    });
    this.currentPage.set(1);
    this.fetchProducts();
  }

  onPageChange(event: { page: number; pageSize: number }) {
    this.currentPage.set(event.page);
    this.pageSize.set(event.pageSize);
    this.fetchProducts();
  }

  handleActionClick(data: { item: Product; actionType: string; extra?: any }) {
    console.log('Action:', data.actionType, 'Product:', data.item, 'Extra:', data.extra);
    switch (data.actionType) {
      case 'view':
        console.log(`Viewing details for ${data.item.name}`);
        break;
      case 'edit':
        console.log(`Editing ${data.item.name} with extra:`, data.extra);
        break;
      case 'delete':
        console.log(`Deleting ${data.item.name}`);
        break;
    }
  }

  /**
   * Xử lý khi selection thay đổi
   * @param selectedIds - Array of selected item IDs
   */
  onSelectionChange(selectedIds: string[]): void {
    this.selectedItems.set(selectedIds);
    console.log('Selected items:', selectedIds);
  }

  /**
   * Xử lý khi action được thực thi
   * @param event - Action execution event
   */
  onActionExecute(event: { actionType: string; selectedItems: string[]; item?: any }): void {
    console.log('Action executed:', event);

    switch (event.actionType) {
      // Default actions
      case 'add-product':
        this.handleAddProduct();
        break;
      case 'import':
        this.handleImport();
        break;

      // Selected actions (bulk operations)
      case 'bulk-delete':
        this.handleBulkDelete(event.selectedItems);
        break;
      case 'bulk-edit':
        this.handleBulkEdit(event.selectedItems);
        break;
      case 'bulk-export':
        this.handleBulkExport(event.selectedItems);
        break;

      // Row actions
      case 'edit':
        this.handleEditProduct(event.item);
        break;
      case 'view-details':
        this.handleViewDetails(event.item);
        break;
      case 'duplicate':
        this.handleDuplicateProduct(event.item);
        break;
      case 'delete':
        this.handleDeleteProduct(event.item);
        break;

      default:
        console.warn('Unknown action type:', event.actionType);
    }
  }

  // ==================== ACTION HANDLERS ====================

  private handleAddProduct(): void {
    console.log('Adding new product...');
    // TODO: Navigate to add product page or open modal
  }

  private handleImport(): void {
    console.log('Importing products...');
    // TODO: Open import dialog
  }

  private handleBulkDelete(selectedIds: string[]): void {
    console.log('Bulk deleting products:', selectedIds);
    // TODO: Show confirmation dialog and delete selected products
  }

  private handleBulkEdit(selectedIds: string[]): void {
    console.log('Bulk editing products:', selectedIds);
    // TODO: Open bulk edit dialog
  }

  private handleBulkExport(selectedIds: string[]): void {
    console.log('Bulk exporting products:', selectedIds);
    // TODO: Export selected products to file
  }

  private handleEditProduct(product: Product): void {
    console.log('Editing product:', product);
    // TODO: Navigate to edit product page or open modal
  }

  private handleViewDetails(product: Product): void {
    console.log('Viewing product details:', product);
    // TODO: Navigate to product details page or open modal
  }

  private handleDuplicateProduct(product: Product): void {
    console.log('Duplicating product:', product);
    // TODO: Create a copy of the product
  }

  private handleDeleteProduct(product: Product): void {
    console.log('Deleting product:', product);
    // TODO: Show confirmation dialog and delete product
  }

  hehe() {
    console.log('ok')
  }
}
