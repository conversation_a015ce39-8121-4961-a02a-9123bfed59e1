import { TemplateRef } from '@angular/core';

export interface Column {
  key: string;
  label: string;
  sortable?: boolean;
}

export interface Filter {
  key: string;
  label: string;
  type: 'search' | 'select' | 'custom';
  options?: { value: string; label: string }[];
  template?: TemplateRef<any>;
}

export interface SortState {
  key: string;
  direction: 'asc' | 'desc' | '';
}

export interface Pager {
  currentPage: number;
  totalPages: number;
  pageSize: number;
}

// Interface cho action item trong menu hoặc action bar
export interface ActionItem {
  key: string;
  label: string;
  icon?: string;
  disabled?: boolean;
  visible?: boolean;
  tooltip?: string;
  actionType: string; // Để identify action khi emit
}

// Interface cho selection state của checkbox
export interface SelectionState {
  selectedItems: string[]; // Array of item IDs
  isAllSelected: boolean;
  isIndeterminate: boolean;
}

// Interface cho row action context
export interface RowActionContext {
  item: any;
  actionType: string;
  extra?: any;
}

export interface ListConfig {
  pageName: string;
  pageShortDescription?: string;
  allColumns: Column[]; // Tất cả cột có thể chọn
  columns: Column[]; // Cột hiện tại (subset của allColumns)
  fixedColumns: Column[];
  filters: Filter[];
  items: any[];
  columnOrder?: string[]; // Thứ tự cột hiển thị
  pager?: Pager;

  // Templates
  fixedColumnTemplate?: TemplateRef<any>;
  scrollableColumnsTemplate?: TemplateRef<any>;

  // Actions templates
  defaultActionsTemplate?: TemplateRef<any>; // Actions khi không có item nào được chọn
  selectedActionsTemplate?: TemplateRef<any>; // Actions khi có items được chọn
  rowActionsTemplate?: TemplateRef<any>; // Actions cho từng row

  // Action items (alternative to templates)
  defaultActions?: ActionItem[];
  selectedActions?: ActionItem[];
  rowActions?: ActionItem[];

  // Selection settings
  enableSelection?: boolean; // Có hiển thị checkbox không
  enableRowActions?: boolean; // Có hiển thị row actions không
}

export interface Product {
  id: string;
  name: string;
  price: number;
  stock: number;
  category: string;
}
