<div class="list-layout">
  <!-- <div>
    Product List
    <i class="fa-solid fa-circle-info"></i>
  </div> -->

  <!-- Thanh filter -->
  <div class="list-layout-header flex flex-nowrap align-items-center">
    <div class="actions-bar">
      <!-- Default actions - hiển thị khi không có item nào được chọn -->
      <div class="flex align-items-center default-actions" *ngIf="!hasSelectedItems()">
        <div
          class="button-24 flex align-items-center"
          [class.active]="isFilterVisible()"
          matTooltip="Filter"
          (click)="toggleFilter()"
          >
          <div class="button__icon">
            <i class="fa-light fa-filter-list"></i>
          </div>
        </div>

        <div
          class="button-24 flex align-items-center"
          matTooltip="Edit Columns"
          (click)="editColumns()"
          >
          <div class="button__icon">
            <i class="fa-light fa-objects-column"></i>
          </div>
        </div>

        <div
          class="button-24 flex align-items-center"
          [matTooltip]="'LIST_LAYOUT.TEXT_SIZE_TOOLTIP' | translate"
          [matMenuTriggerFor]="textSizeMenu"
          >
          <div class="button__icon">
            <i class="fa-light fa-text-size"></i>
          </div>
        </div>

        <!-- Default actions từ config -->
        <ng-container *ngIf="config.defaultActionsTemplate">
          <ng-container *ngTemplateOutlet="config.defaultActionsTemplate"></ng-container>
        </ng-container>

        <!-- Default actions từ config.defaultActions -->
        <ng-container *ngIf="config.defaultActions && !config.defaultActionsTemplate">
          <div
            *ngFor="let action of config.defaultActions"
            class="button-24 flex align-items-center"
            [matTooltip]="action.tooltip || action.label"
            (click)="onDefaultActionClick(action.actionType)"
            [class.disabled]="action.disabled"
            [style.display]="action.visible === false ? 'none' : 'flex'"
            >
            <div class="button__icon">
              <i [class]="action.icon" *ngIf="action.icon"></i>
            </div>
            <span class="ms-2" *ngIf="action.label">{{ action.label | translate }}</span>
          </div>
        </ng-container>

        <div class="page-list-info flex align-items-center">
          {{ config.pageName | translate}}

          @if(config.pageShortDescription) {
          <div class="button__icon ms-3">
            <i
            class="fa-solid fa-circle-info"
            [matTooltip]="(config.pageShortDescription || '') | translate"
            >
          </i>
          </div>
          }
        </div>
      </div>

      <!-- Selected actions - hiển thị khi có items được chọn -->
      <div class="flex align-items-center selected-items-actions" *ngIf="hasSelectedItems()">
        <div class="selected-count me-3">
          <span>{{ 'LIST_LAYOUT.SELECTED_ITEMS' | translate: {count: getSelectedCount()} }}</span>
        </div>

        <!-- Selected actions từ config -->
        <ng-container *ngIf="config.selectedActionsTemplate">
          <ng-container *ngTemplateOutlet="config.selectedActionsTemplate; context: { selectedItems: selectedItems() }"></ng-container>
        </ng-container>

        <!-- Selected actions từ config.selectedActions -->
        <ng-container *ngIf="config.selectedActions && !config.selectedActionsTemplate">
          <div
            *ngFor="let action of config.selectedActions"
            class="button-24 flex align-items-center"
            [matTooltip]="action.tooltip || action.label"
            (click)="onSelectedActionClick(action.actionType)"
            [class.disabled]="action.disabled"
            [style.display]="action.visible === false ? 'none' : 'flex'"
            >
            <div class="button__icon">
              <i [class]="action.icon" *ngIf="action.icon"></i>
            </div>
            <span class="ms-2" *ngIf="action.label">{{ action.label | translate }}</span>
          </div>
        </ng-container>

        <!-- Clear selection button -->
        <div
          class="button-24 flex align-items-center ms-3"
          [matTooltip]="'LIST_LAYOUT.CLEAR_SELECTION' | translate"
          (click)="clearSelection()"
          >
          <div class="button__icon">
            <i class="fa-light fa-times"></i>
          </div>
        </div>
      </div>


      <!-- <div class="button-24 flex align-items-center">
        <div class="button__text pe-3">
          <i class="fa-solid fa-square-check"></i>
          <span class="mx-2">Đã chọn (5)</span>
          <i class="fa-regular fa-chevron-down"></i>
        </div>
      </div> -->
    </div>


    <div class="ms-auto">
     <!-- MatPaginator -->
      <div #paginatorEle>
      <mat-paginator
        *ngIf="config.pager"
        [pageIndex]="config.pager.currentPage - 1"
        [pageSize]="config.pager.pageSize"
        [length]="config.pager.totalPages * config.pager.pageSize"
        [pageSizeOptions]="pageSizeOptions"
        (page)="onPageChange($event)">
      </mat-paginator>
      </div>
    </div>
  </div>



  <!-- Danh sách -->
  <div class="list-container" #listContainer [class]="'size--' + currentFontSize()">
    <!-- Filter panel với CSS animation thông qua directive -->
    <div
      class="field-filters"
      [class.show]="isFilterVisible()"
      #filter
      >
      <div
        class="relative h-100"
        *ngIf="isFilterVisible()"
        >
        <app-field-filters
          [fields]="mockFields"
          [showTitle]="true"
          [showClearAll]="true"
          (filterChange)="onFieldFilterChange($event)"
          (filtersApplied)="onFiltersApplied($event)"
          (filtersReset)="onFiltersReset()">
        </app-field-filters>
      </div>

      <div
        appResizePanel
        class="panel-resize-handle resize-handle--absolute"
        [leftPanel]="filter"
        [rightPanel]="list"
        [showLeftPanel]="isFilterVisible()"
        [minWidth]="300"
        [maxWidth]="600"
        [additionalMarginLeft]="20"
        [isAbsolute]="true"
        [panelName]="'list-layout'">
      </div>
    </div>




    <div
      class="table-wrapper"
      #list>
      <!-- Block trái: Fixed columns -->
      <div class="fixed-columns" #fixedColumns>
        <div class="fixed-header">
          <!-- Row actions column header -->
          <div class="fixed-column actions" *ngIf="config.enableRowActions !== false"></div>

          <!-- Toggle checkbox column header -->
          <div class="fixed-column tick-box toggle-tick-box" *ngIf="config.enableSelection !== false">
            <mat-checkbox
              [checked]="isAllSelected()"
              [indeterminate]="isIndeterminate()"
              (change)="toggleAllItems()"
              [matTooltip]="'LIST_LAYOUT.TOGGLE_ALL_SELECTION' | translate"
              [class.no-animation]="isBulkOperation()">
            </mat-checkbox>
          </div>

          <!-- Fixed columns headers -->
          <div *ngFor="let col of config.fixedColumns" class="fixed-column" (click)="sortColumn(col.key)">
            {{ col.label }}
            <span *ngIf="sortState().key === col.key && sortState().direction">
              {{ sortState().direction === 'asc' ? '↑' : '↓' }}
            </span>
          </div>
        </div>
        <div class="fixed-body">
          <div
            class="fixed-row"
            [class.hover]="hoverState()[item.id]"
            [class.selected]="isItemSelected(item)"
            (mouseenter)="onRowHover(item)"
            (mouseleave)="onRowLeave(item)"
            *ngFor="let item of config.items; trackBy: trackById"
            >
            <!-- Row actions column -->
            <div class="fixed-column actions" *ngIf="config.enableRowActions !== false">
              <div class="icon" matRipple [matMenuTriggerFor]="rowActionsMenu" #menuTrigger="matMenuTrigger">
                <mat-icon>more_horiz</mat-icon>
              </div>

              <!-- Row actions menu -->
              <mat-menu #rowActionsMenu="matMenu">
                <!-- Row actions từ template -->
                <ng-container *ngIf="config.rowActionsTemplate">
                  <ng-container *ngTemplateOutlet="config.rowActionsTemplate; context: { $implicit: item }"></ng-container>
                </ng-container>

                <!-- Row actions từ config.rowActions -->
                <ng-container *ngIf="config.rowActions && !config.rowActionsTemplate">
                  <button
                    *ngFor="let action of config.rowActions"
                    mat-menu-item
                    [disabled]="action.disabled"
                    [style.display]="action.visible === false ? 'none' : 'block'"
                    (click)="onRowActionClick(action.actionType, item)">
                    <mat-icon *ngIf="action.icon">{{ action.icon }}</mat-icon>
                    <span>{{ action.label | translate }}</span>
                  </button>
                </ng-container>
              </mat-menu>
            </div>

            <!-- Checkbox column -->
            <div class="fixed-column tick-box" *ngIf="config.enableSelection !== false">
              <mat-checkbox
                [checked]="isItemSelected(item)"
                (change)="toggleItemSelection(item)"
                [class.no-animation]="isBulkOperation()">
              </mat-checkbox>
            </div>

            <ng-container *ngIf="config.fixedColumnTemplate; else defaultFixedColumns">
              <ng-container *ngTemplateOutlet="config.fixedColumnTemplate; context: { $implicit: item, fixedColumns: config.fixedColumns }"></ng-container>
            </ng-container>
            <ng-template #defaultFixedColumns>
              <div *ngFor="let col of config.fixedColumns" class="fixed-column">
                {{ item[col.key] }}
              </div>
            </ng-template>
          </div>
        </div>
      </div>

      <!-- Block phải: Scrollable columns + Actions -->
      <div class="scrollable-container" #scrollableContainer>
        <!-- Header sticky -->
        <div class="scrollable-header" #scrollableHeader>
          <div *ngFor="let col of columnOrder()" class="column-header" (click)="sortColumn(col)">
            {{ getColumnLabel(col) }}
            <span *ngIf="sortState().key === col && sortState().direction">
              {{ sortState().direction === 'asc' ? '↑' : '↓' }}
            </span>
          </div>
        </div>
        <!-- Body cuộn ngang bằng Swiper -->
        <div class="scrollable-section swiper" #scrollableSection>
          <div class="swiper-wrapper">
            <div class="scrollable-table swiper-slide">
              <div class="scrollable-body">
                <div
                  class="scrollable-row"
                  [class.hover]="hoverState()[item.id]"
                  [class.selected]="isItemSelected(item)"
                  (mouseenter)="onRowHover(item)"
                  (mouseleave)="onRowLeave(item)"
                  *ngFor="let item of config.items; trackBy: trackById">
                  <ng-container *ngIf="config.scrollableColumnsTemplate; else defaultScrollableColumns">
                    <ng-container *ngTemplateOutlet="config.scrollableColumnsTemplate; context: { $implicit: item, visibleColumns: columnOrder() }"></ng-container>
                  </ng-container>
                  <ng-template #defaultScrollableColumns>
                    <div *ngFor="let col of columnOrder()" class="column-data">
                      {{ item[col] }}
                    </div>
                  </ng-template>
                </div>
              </div>
            </div>
          </div>

          <div class="swiper-scrollbar" #scrollableScrollbar></div>
        </div>
      </div>
    </div>
  </div>
</div>



<!-- Text Size Menu -->
<mat-menu #textSizeMenu="matMenu" class="text-size-menu">
  <div class="text-size-menu-content" (click)="$event.stopPropagation()">
    <div class="text-size-header">
      <span>{{ 'LIST_LAYOUT.TEXT_SIZE' | translate }}</span>
      <span class="text-size-current">{{ 'LIST_LAYOUT.TEXT_SIZE_CURRENT' | translate: {size: formatSliderValue(currentSliderValue())} }}</span>
    </div>

    <div class="text-size-slider-container">
      <mat-slider
        [min]="0"
        [max]="4"
        [step]="1"
        [discrete]="true"
        [showTickMarks]="true">
        <input
          matSliderThumb
          [value]="currentSliderValue()"
          (valueChange)="onFontSizeSliderChange($event)">
      </mat-slider>
    </div>

    <div class="text-size-labels">
      <span class="size-label">A--</span>
      <span class="size-label">A-</span>
      <span class="size-label">0</span>
      <span class="size-label">A+</span>
      <span class="size-label">A++</span>
    </div>
  </div>
</mat-menu>
