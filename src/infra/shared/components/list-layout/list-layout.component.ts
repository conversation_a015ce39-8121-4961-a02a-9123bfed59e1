import { Component, EventEmitter, Input, Output, signal, TemplateRef, ChangeDetectionStrategy, ViewChild, ViewEncapsulation, ElementRef, OnChanges, OnInit, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ListConfig, SortState, SelectionState, ActionItem, RowActionContext } from '../../models/view/list-layout.model';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { Swiper } from 'swiper';
import { getElementMaxHeightToFit100vh } from '@/shared/utils';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { ListColumnSelectorModalService } from '@/shared/modals/common/list-column-selector-modal/list-column-selector-modal.service';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatMenuModule } from '@angular/material/menu';
import { MatSliderModule } from '@angular/material/slider';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { mockFields } from '@/mock/fields.mock';
import { FieldFiltersComponent } from '../field-filters/field-filters.component';
import { FieldFilter, FilterChangeEvent } from '../field-filters/models/view/field-filter-view.model';
import { ResizePanelDirective } from '@/shared/directives/resize-panel/resize-panel.directive';
import {MatCheckboxModule} from '@angular/material/checkbox';
import { MatRippleModule } from '@angular/material/core';

// Interface cho font size values
export type FontSizeValue = 'A--' | 'A-' | '0' | 'A+' | 'A++';

export interface FontSizeOption {
  value: FontSizeValue;
  label: string;
  sliderValue: number;
}

@Component({
  selector: 'app-list-layout',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatPaginatorModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatSelectModule,
    MatTooltipModule,
    MatMenuModule,
    MatSliderModule,
    MatButtonModule,
    TranslateModule,
    FieldFiltersComponent,
    ResizePanelDirective,
    MatCheckboxModule,
    MatRippleModule
  ],
  templateUrl: './list-layout.component.html',
  styleUrls: ['./list-layout.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class ListLayoutComponent implements OnInit, AfterViewInit, OnChanges {
  @Input() config!: ListConfig;

  @Output() filterChange = new EventEmitter<Record<string, any>>();
  @Output() sortChange = new EventEmitter<SortState>();
  @Output() onActionClick = new EventEmitter<{ item: any; actionType: string; extra?: any }>();
  @Output() columnsChange = new EventEmitter<{ visibleColumns: string[]; columnOrder: string[] }>();
  @Output() pageChange = new EventEmitter<{ page: number; pageSize: number }>();

  // Selection events
  @Output() selectionChange = new EventEmitter<string[]>(); // Emit selected item IDs
  @Output() actionExecute = new EventEmitter<{ actionType: string; selectedItems: string[]; item?: any }>();

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild('filter', { static: true }) filter!: ElementRef<HTMLElement>;
  @ViewChild('list', { static: true }) list!: ElementRef<HTMLElement>;
  @ViewChild(ResizePanelDirective, { static: false }) resizePanelDirective!: ResizePanelDirective;

  @ViewChild('fixedColumns', { static: true }) fixedColumns!: ElementRef<any>;
  @ViewChild('scrollableContainer', { static: true }) scrollableContainer!: ElementRef<any>;
  @ViewChild('scrollableSection', { static: true }) scrollableSection!: ElementRef<any>;
  @ViewChild('scrollableHeader', { static: true }) scrollableHeader!: ElementRef<any>;
  @ViewChild('scrollableScrollbar', { static: true }) scrollableScrollbar!: ElementRef<any>;
  @ViewChild('listContainer', { static: true }) listContainer!: ElementRef<any>;
  @ViewChild('paginatorEle', { static: true }) paginatorEle!: ElementRef<any>;

  visibleColumns = signal<string[]>([]);
  filterValues = signal<Record<string, any>>({});
  sortState = signal<SortState>({ key: '', direction: '' });
  columnOrder = signal<string[]>([]);
  pageSizeOptions = [2, 5, 10, 20];
  hoverState = signal<Record<string, boolean>>({});

  // Filter visibility state management
  isFilterVisible = signal<boolean>(true);

  // Selection state management
  selectedItems = signal<string[]>([]); // Array of selected item IDs
  isAllSelected = signal<boolean>(false); // Toggle checkbox state
  isIndeterminate = signal<boolean>(false); // Toggle checkbox indeterminate state
  isBulkOperation = signal<boolean>(false); // Track bulk operations to disable animations

  // Text size management
  currentFontSize = signal<FontSizeValue>('0');
  currentSliderValue = signal<number>(2); // Default to middle value (index 2 = '0')

  // Font size options với mapping giữa slider value và font size
  fontSizeOptions: FontSizeOption[] = [
    { value: 'A--', label: 'LIST_LAYOUT.TEXT_SIZE_SMALLEST', sliderValue: 0 },
    { value: 'A-', label: 'LIST_LAYOUT.TEXT_SIZE_SMALL', sliderValue: 1 },
    { value: '0', label: 'LIST_LAYOUT.TEXT_SIZE_NORMAL', sliderValue: 2 },
    { value: 'A+', label: 'LIST_LAYOUT.TEXT_SIZE_LARGE', sliderValue: 3 },
    { value: 'A++', label: 'LIST_LAYOUT.TEXT_SIZE_LARGEST', sliderValue: 4 }
  ];

  mockFields = mockFields;


  private swiperInstance?: Swiper;

  constructor(
    private listColumnSelectorModalService: ListColumnSelectorModalService
  ) {

  }
  ngOnInit() {
    // Khởi tạo visibleColumns và columnOrder, loại bỏ fixedColumns
    const nonFixedColumns = this.config.columns.filter(col => !this.config.fixedColumns.some(fc => fc.key === col.key));
    this.visibleColumns.set(nonFixedColumns.map(col => col.key));
    this.columnOrder.set(this.config.columnOrder || nonFixedColumns.map(col => col.key));


    // Khởi tạo font size từ localStorage
    this.initializeFontSize();

    // this.editColumns()
  }

  ngAfterViewInit() {
    // Đồng bộ trạng thái MatPaginator
    if (this.config.pager) {
      this.paginator.pageIndex = this.config.pager.currentPage - 1;
      this.paginator.pageSize = this.config.pager.pageSize;
      this.paginator.length = this.config.pager.totalPages * this.config.pager.pageSize;
    }


    // Áp dụng font size class sau khi view đã được khởi tạo
    setTimeout(() => {
      this.applyFontSizeClass();
    }, 0);

    this.initSwiper();
  }

  initSwiper() {
    if(!this.swiperInstance &&
      this.listContainer?.nativeElement &&
      this.scrollableContainer?.nativeElement &&
      this.fixedColumns?.nativeElement
    ) {
      const paginatorHeight = (this.paginatorEle?.nativeElement?.offsetHeight || 0) + 20;
      const maxHeight = getElementMaxHeightToFit100vh(this.listContainer.nativeElement, 10);
      this.listContainer.nativeElement.style.setProperty('max-height', `${maxHeight}px`);

      const fixedColumnWidth = this.fixedColumns?.nativeElement?.offsetWidth || 0;
      this.scrollableContainer.nativeElement!.style.width = `calc(100% - ${fixedColumnWidth}px)`;



      this.swiperInstance = new Swiper(this.scrollableSection.nativeElement, {
        slidesPerView: 'auto',
        freeMode: {
          enabled: true,
          momentumRatio: 1, // Đảm bảo animation mượt, không chậm
        },
        scrollbar: {
          enabled: true,
          el: this.scrollableScrollbar?.nativeElement,
          hide: false,
          draggable: true,
          snapOnRelease: true,
        },
        // mousewheel = trackpad 2 ngon
        mousewheel: {
          enabled: true,
          forceToAxis: true,
          sensitivity: 1,
          releaseOnEdges: true
        },
        resistanceRatio: 0.5,
        touchRatio: 1,
        // simulateTouch: true
      });


      this.swiperInstance.on('setTranslate', () => {
        if (this.scrollableHeader?.nativeElement && this.swiperInstance) {
          requestAnimationFrame(() => {
            this.scrollableHeader.nativeElement.style.transform = `translateX(${this.swiperInstance!.translate}px)`;
          });
        }
      });
    }
  }

  trackById(index: number, item: any): string {
    return item.id;
  }

  /**
   * Toggle filter visibility với horizontal resize animation
   * Sequence cho SHOWING: render component -> animate width expansion
   * Sequence cho HIDING: animate width collapse -> remove component
   */
  toggleFilter() {
    const currentlyVisible = this.isFilterVisible();

    if (!currentlyVisible) {
      // SHOWING sequence: render first, then animate
      this.showFilter();
    } else {
      // HIDING sequence: animate first, then remove
      this.hideFilter();
    }
  }

  /**
   * Show filter với CSS animation thông qua directive
   */
  private async showFilter(): Promise<void> {
    // Step 1: Render component (set ngIf to true)
    this.isFilterVisible.set(true);

    // Step 2: Wait for DOM render, then trigger directive animation
    // Use requestAnimationFrame for better timing coordination
    requestAnimationFrame(() => {
      requestAnimationFrame(async () => {
        if (this.resizePanelDirective) {
          await this.resizePanelDirective.show();
        }
      });
    });
  }

  /**
   * Hide filter với CSS animation thông qua directive
   */
  private async hideFilter(): Promise<void> {
    this.isFilterVisible.set(false);

    // Trigger directive animation và wait for completion
    if (this.resizePanelDirective) {
      await this.resizePanelDirective.hide();
    }
  }



  onFilterChange(key: string, value: any) {
    this.filterValues.set({ ...this.filterValues(), [key]: value });
    this.filterChange.emit(this.filterValues());
  }

  sortColumn(key: string) {
    const column = this.config.allColumns.find(c => c.key === key) || this.config.fixedColumns.find(c => c.key === key);
    if (!column?.sortable) return;
    const currentSort = this.sortState();
    let direction: 'asc' | 'desc' | '' = '';
    if (currentSort.key === key) {
      direction = currentSort.direction === 'asc' ? 'desc' : currentSort.direction === 'desc' ? '' : 'asc';
    } else {
      direction = 'asc';
    }
    this.sortState.set({ key, direction });
    this.sortChange.emit({ key, direction });
  }

  handleActionClick(item: any, actionType: string, extra?: any) {
    this.onActionClick.emit({ item, actionType, extra });
  }

  onColumnsChange({ visibleColumns, columnOrder }: { visibleColumns: string[]; columnOrder: string[] }) {
    this.visibleColumns.set(visibleColumns);
    this.columnOrder.set(columnOrder);
    this.columnsChange.emit({ visibleColumns, columnOrder });
  }

  getColumnLabel(key: string): string {
    return (
      this.config.allColumns.find(c => c.key === key)?.label ||
      this.config.fixedColumns.find(c => c.key === key)?.label ||
      ''
    );
  }

  onPageChange(event: { pageIndex: number; pageSize: number }) {
    const page = event.pageIndex + 1;
    const pageSize = event.pageSize;
    this.pageChange.emit({ page, pageSize });
  }

  onRowHover(item: any) {
    this.hoverState.set({ ...this.hoverState(), [item.id]: true });
  }

  onRowLeave(item: any) {
    this.hoverState.set({ ...this.hoverState(), [item.id]: false });
  }

  editColumns() {
    this.listColumnSelectorModalService.open({
      allColumns: this.config.allColumns,
      regularColumns: this.visibleColumns(),
      pinnedColumns: [],
      maxPinnedColumns: 3
    });
  }

  // ==================== SELECTION METHODS ====================

  /**
   * Kiểm tra xem một item có được chọn không
   * @param item - Item cần kiểm tra
   * @returns true nếu item được chọn
   */
  isItemSelected(item: any): boolean {
    return this.selectedItems().includes(item.id);
  }

  /**
   * Toggle selection cho một item
   * @param item - Item cần toggle
   */
  toggleItemSelection(item: any): void {
    const currentSelected = this.selectedItems();
    const itemId = item.id;

    if (currentSelected.includes(itemId)) {
      // Bỏ chọn item
      const newSelected = currentSelected.filter(id => id !== itemId);
      this.selectedItems.set(newSelected);
    } else {
      // Chọn item
      this.selectedItems.set([...currentSelected, itemId]);
    }

    this.updateToggleCheckboxState();
    this.selectionChange.emit(this.selectedItems());
  }

  /**
   * Toggle tất cả items trên trang hiện tại
   */
  toggleAllItems(): void {
    // Set bulk operation flag để disable animations
    this.isBulkOperation.set(true);

    const currentPageItems = this.config.items || [];
    const currentPageItemIds = currentPageItems.map(item => item.id);
    const currentSelected = this.selectedItems();

    // Đếm số items được chọn trên trang hiện tại
    const selectedOnCurrentPage = currentPageItemIds.filter(id => currentSelected.includes(id));

    // Xác định hành động dựa trên trạng thái hiện tại:
    // - Nếu không có item nào được chọn (empty): chọn tất cả
    // - Nếu tất cả items được chọn (checked): bỏ chọn tất cả
    // - Nếu một số items được chọn (indeterminate): bỏ chọn tất cả
    const shouldSelectAll = selectedOnCurrentPage.length === 0;

    if (shouldSelectAll) {
      // Chọn tất cả items trên trang hiện tại (giữ lại selections từ trang khác)
      const itemsFromOtherPages = currentSelected.filter(id => !currentPageItemIds.includes(id));
      this.selectedItems.set([...itemsFromOtherPages, ...currentPageItemIds]);
    } else {
      // Bỏ chọn tất cả items trên trang hiện tại (bao gồm cả trường hợp indeterminate)
      const newSelected = currentSelected.filter(id => !currentPageItemIds.includes(id));
      this.selectedItems.set(newSelected);
    }

    this.updateToggleCheckboxState();
    this.selectionChange.emit(this.selectedItems());

    // Reset bulk operation flag sau một delay ngắn để animations có thể hoạt động trở lại
    setTimeout(() => {
      this.isBulkOperation.set(false);
    }, 100);
  }

  /**
   * Cập nhật trạng thái của toggle checkbox dựa trên selections hiện tại
   */
  updateToggleCheckboxState(): void {
    const currentPageItems = this.config.items || [];
    const currentPageItemIds = currentPageItems.map(item => item.id);
    const currentSelected = this.selectedItems();

    // Đếm số items được chọn trên trang hiện tại
    const selectedOnCurrentPage = currentPageItemIds.filter(id => currentSelected.includes(id));

    console.log('selectedOnCurrentPage', selectedOnCurrentPage)
    console.log('currentPageItemIds', currentPageItemIds)

    if (selectedOnCurrentPage.length === 0) {
      // Không có item nào được chọn
      this.isAllSelected.set(false);
      this.isIndeterminate.set(false);
    } else if (selectedOnCurrentPage.length === currentPageItemIds.length) {
      // Tất cả items trên trang được chọn
      this.isAllSelected.set(true);
      this.isIndeterminate.set(false);
    } else {
      // Một số items được chọn (indeterminate state)
      this.isAllSelected.set(false);
      this.isIndeterminate.set(true);
    }

    console.log(this.isAllSelected(), this.isIndeterminate())
  }

  // ==================== ACTION METHODS ====================

  /**
   * Xử lý khi action được click từ default actions
   * @param actionType - Loại action
   */
  onDefaultActionClick(actionType: string): void {
    this.actionExecute.emit({
      actionType,
      selectedItems: []
    });
  }

  /**
   * Xử lý khi action được click từ selected actions
   * @param actionType - Loại action
   */
  onSelectedActionClick(actionType: string): void {
    this.actionExecute.emit({
      actionType,
      selectedItems: this.selectedItems()
    });
  }

  /**
   * Xử lý khi action được click từ row actions
   * @param actionType - Loại action
   * @param item - Item hiện tại
   */
  onRowActionClick(actionType: string, item: any): void {
    this.actionExecute.emit({
      actionType,
      selectedItems: [],
      item
    });
  }

  /**
   * Kiểm tra xem có hiển thị selected actions không
   * @returns true nếu có items được chọn
   */
  hasSelectedItems(): boolean {
    return this.selectedItems().length > 0;
  }

  /**
   * Lấy số lượng items được chọn
   * @returns Số lượng items được chọn
   */
  getSelectedCount(): number {
    return this.selectedItems().length;
  }

  /**
   * Clear tất cả selections
   */
  clearSelection(): void {
    this.selectedItems.set([]);
    this.updateToggleCheckboxState();
    this.selectionChange.emit([]);
  }

  // ==================== LIFECYCLE HOOKS ====================

  /**
   * Cập nhật toggle checkbox state khi config.items thay đổi (pagination)
   */
  ngOnChanges(): void {
    // Cập nhật toggle checkbox state khi items thay đổi (do pagination)
    this.updateToggleCheckboxState();
  }

  /**
   * Xử lý khi filter thay đổi
   * @param event - Filter change event
   */
  onFieldFilterChange(event: FilterChangeEvent): void {
    // console.log('Field filter changed:', event);

    // // Tìm field tương ứng
    // const field = this.mockFields.find(f => f._id === event.fieldId);
    // if (field) {
    //   const status = event.isActive ? 'activated' : 'deactivated';
    //   console.log(`Filter for "${field.label}" ${status}`);

    //   if (event.isActive && event.filterValue) {
    //     console.log('Filter value:', event.filterValue);
    //   }
    // }
  }

  /**
   * Xử lý khi các filters được áp dụng
   * @param activeFilters - Danh sách các filter đang active
   */
  onFiltersApplied(activeFilters: FieldFilter[]): void {
    console.log('Active filters applied:', activeFilters);

    if (activeFilters.length > 0) {
      const filterSummary = activeFilters.map(filter => {
        const operator = filter.filterValue?.operator || 'unknown';
        const value = this.getFilterValueDisplay(filter.filterValue);
        return `${filter.field.label}: ${operator}${value ? ` (${value})` : ''}`;
      }).join(', ');

      console.log(`Applied ${activeFilters.length} filter(s): ${filterSummary}`);
    } else {
      console.log('No filters applied');
    }
  }

  /**
   * Xử lý khi filters được reset
   */
  onFiltersReset(): void {
    console.log('Filters have been reset');
  }

  /**
   * Helper method để hiển thị giá trị filter
   * @param filterValue - Giá trị filter
   * @returns String representation của giá trị
   */
  private getFilterValueDisplay(filterValue: any): string {
    if (!filterValue) return '';

    if (filterValue.value !== undefined) {
      return String(filterValue.value);
    }

    if (filterValue.values && Array.isArray(filterValue.values)) {
      return filterValue.values.join(', ');
    }

    if (filterValue.minValue !== undefined && filterValue.maxValue !== undefined) {
      return `${filterValue.minValue} - ${filterValue.maxValue}`;
    }

    if (filterValue.timeValue !== undefined && filterValue.timeUnit) {
      return `${filterValue.timeValue} ${filterValue.timeUnit}`;
    }

    return '';
  }

  /**
   * Khởi tạo font size từ localStorage
   * Đọc giá trị đã lưu hoặc sử dụng giá trị mặc định
   */
  private initializeFontSize(): void {
    const savedFontSize = localStorage.getItem('listLayoutFontSize') as FontSizeValue;

    if (savedFontSize && this.fontSizeOptions.some(option => option.value === savedFontSize)) {
      // Nếu có giá trị hợp lệ trong localStorage, sử dụng nó
      this.setFontSize(savedFontSize);
    } else {
      // Nếu không có hoặc giá trị không hợp lệ, sử dụng giá trị mặc định '0'
      this.setFontSize('0');
    }
  }

  /**
   * Set font size và cập nhật slider value tương ứng
   * @param fontSize - Font size value để set
   */
  private setFontSize(fontSize: FontSizeValue): void {
    const option = this.fontSizeOptions.find(opt => opt.value === fontSize);
    if (option) {
      this.currentFontSize.set(fontSize);
      this.currentSliderValue.set(option.sliderValue);
      this.applyFontSizeClass();
    }
  }

  /**
   * Xử lý khi slider thay đổi
   * @param sliderValue - Giá trị slider (0-4)
   */
  onFontSizeSliderChange(sliderValue: number): void {
    const option = this.fontSizeOptions.find(opt => opt.sliderValue === sliderValue);
    if (option) {
      this.currentFontSize.set(option.value);
      this.currentSliderValue.set(sliderValue);

      // Lưu vào localStorage
      localStorage.setItem('listLayoutFontSize', option.value);

      // Áp dụng CSS class
      this.applyFontSizeClass();
    }
  }

  /**
   * Áp dụng CSS class cho font size vào listContainer
   */
  private applyFontSizeClass(): void {
    if (this.listContainer?.nativeElement) {
      const element = this.listContainer.nativeElement;

      // Xóa tất cả các class font size cũ
      this.fontSizeOptions.forEach(option => {
        element.classList.remove(`size--${option.value}`);
      });

      // Thêm class mới
      element.classList.add(`size--${this.currentFontSize()}`);
    }
  }

  /**
   * Lấy label hiển thị cho font size hiện tại
   * @returns Translation key cho label
   */
  getCurrentFontSizeLabel(): string {
    const option = this.fontSizeOptions.find(opt => opt.value === this.currentFontSize());
    return option?.label || 'LIST_LAYOUT.TEXT_SIZE_NORMAL';
  }

  /**
   * Format slider value để hiển thị (0-4 -> A--, A-, 0, A+, A++)
   * @param value - Slider value
   * @returns Display value
   */
  formatSliderValue(value: number): string {
    const option = this.fontSizeOptions.find(opt => opt.sliderValue === value);
    return option?.value || '0';
  }
}
