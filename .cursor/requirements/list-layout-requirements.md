Dưới đây là diễn giải chi tiết yêu cầu của bạn để một AI như Cursor có thể hiểu rõ và hỗ trợ triển khai các thay đổi cần thiết trong `ListLayoutComponent`. <PERSON>êu cầu được trình bày một cách rõ ràng, không chứa code, và tập trung vào mô tả chức năng mong muốn một cách chi tiết, phù hợp với bối cảnh của hệ thống ERP mà bạn đang phát triển, đặc biệt là các module như IMS, WMS, OMS, và ECOMMERCE, nhắm đến các cửa hàng bán hàng online trên các nền tảng như Shopee, TikTok Shop, Lazada, v.v.

---

### **<PERSON><PERSON><PERSON> cầu tổng quan**
`ListLayoutComponent` là một component Angular dùng để hiển thị danh sách dữ liệu (ví dụ: danh sách sản phẩm trong module IMS hoặc OMS) và các hành động (actions) liên quan. Component này được thiết kế để tái sử dụng, cho phép các component con (child components) như danh sách sản phẩm, đơn hàng, hoặc kho hàng kế thừa và tùy chỉnh. Hiện tại, bạn cần cải tiến các chức năng liên quan đến:

1. **Hệ thống checkbox (tickbox)** để chọn/tick các mục trong danh sách.
2. **Thanh hành động (actions bar)** hiển thị động dựa trên trạng thái chọn mục.
3. **Hành động trên từng dòng (row)** của danh sách sản phẩm, với menu hành động tùy chỉnh.

Mục tiêu là đảm bảo giao diện và chức năng trực quan, hỗ trợ tốt cho các cửa hàng bán hàng đa kênh (Shopee, TikTok Shop, Lazada, v.v.) và tích hợp tốt với các module ERP như quản lý kho (IMS), quản lý đơn hàng (OMS), hoặc tích hợp thương mại điện tử (ECOMMERCE).

---

### **1. Yêu cầu cải tiến hệ thống checkbox (tickbox)**

#### **Mô tả chức năng hiện tại**
- Trong giao diện danh sách (`ListLayoutComponent`), có một checkbox tổng (`toggle-tick-box`) ở phần tiêu đề (header) của cột cố định (`fixed-column`) để kiểm soát việc chọn tất cả các mục trong danh sách.
- Mỗi dòng (row) trong danh sách sản phẩm cũng có một checkbox riêng để chọn từng mục.
- Hiện tại, danh sách có hỗ trợ phân trang (pagination) thông qua `MatPaginator`, nhưng không rõ liệu trạng thái chọn checkbox có được duy trì khi người dùng chuyển trang hay không.

#### **Yêu cầu cải tiến**
- **Trạng thái mặc định của checkbox tổng (`toggle-tick-box`)**:
  - Ban đầu, checkbox tổng hiển thị trạng thái **trống** (chưa được chọn).
  - Khi người dùng nhấp vào checkbox tổng, **tất cả các mục** trong danh sách sản phẩm trên **trang hiện tại** sẽ được chọn (tick).

- **Hành vi khi người dùng chọn một hoặc nhiều mục riêng lẻ**:
  - Nếu người dùng chọn ít nhất một checkbox trong danh sách sản phẩm (nhưng không phải toàn bộ), checkbox tổng sẽ hiển thị biểu tượng **dấu gạch ngang** (`-`) để biểu thị trạng thái chọn một phần (indeterminate state).
  - Khi nhấp vào checkbox tổng trong trạng thái này, **tất cả các mục** trong danh sách trên **trang hiện tại** sẽ được **bỏ chọn** (untick).

- **Hành vi khi tất cả các mục được chọn**:
  - Nếu người dùng đã chọn **toàn bộ các mục** trong danh sách trên trang hiện tại (bằng cách chọn từng checkbox hoặc nhấp vào checkbox tổng), checkbox tổng sẽ hiển thị trạng thái **đã tick** (checked state, sử dụng biểu tượng của `mat-checkbox` trong trạng thái được chọn).
  - Nếu người dùng nhấp lại vào checkbox tổng trong trạng thái này, **tất cả các mục** trong danh sách trên trang hiện tại sẽ được **bỏ chọn**.

- **Duy trì trạng thái chọn qua các trang (pagination)**:
  - Khi người dùng chọn một hoặc nhiều sản phẩm trên một trang, sau đó chuyển sang trang khác (bằng cách nhấp vào nút next/prev của `MatPaginator`), trạng thái chọn của các sản phẩm đã tick **phải được giữ nguyên**.
  - Đề xuất: Lưu trữ danh sách các mục đã chọn (dựa trên `id` của sản phẩm hoặc một thuộc tính duy nhất) trong một **mảng** (ví dụ: `selectedItems`) trong component. Mảng này sẽ được cập nhật mỗi khi người dùng chọn hoặc bỏ chọn một mục.
  - Khi người dùng quay lại trang trước, các mục đã chọn trước đó sẽ vẫn hiển thị trạng thái **đã tick**. Checkbox tổng sẽ được cập nhật dựa trên trạng thái của các mục trên trang hiện tại (trống, dấu gạch ngang, hoặc đã tick).

#### **Kịch bản sử dụng**
- Người dùng vào danh sách sản phẩm (ví dụ: module IMS), thấy checkbox tổng ở trạng thái trống.
- Người dùng tick một vài sản phẩm → Checkbox tổng hiển thị dấu gạch ngang.
- Người dùng nhấp vào checkbox tổng → Tất cả sản phẩm trên trang hiện tại được bỏ chọn.
- Người dùng tick toàn bộ sản phẩm trên trang → Checkbox tổng hiển thị trạng thái đã tick.
- Người dùng chuyển sang trang tiếp theo → Các sản phẩm đã chọn ở trang trước vẫn được lưu trong mảng `selectedItems` và hiển thị trạng thái tick khi quay lại.

#### **Lưu ý kỹ thuật**
- Sử dụng một mảng (`selectedItems`) để lưu trữ các mục đã chọn, dựa trên thuộc tính `id` của sản phẩm (theo interface `Product` trong `list-layout.model.js`).
- Cập nhật trạng thái của checkbox tổng dựa trên số lượng mục đã chọn trên trang hiện tại so với tổng số mục trên trang.
- Đảm bảo đồng bộ trạng thái checkbox khi người dùng chuyển trang bằng cách kiểm tra `id` của các mục trong `selectedItems` khi render lại danh sách.

---

### **2. Xử lý thanh hành động (actions bar)**

#### **Mô tả chức năng hiện tại**
- Component hiện có một khu vực hiển thị các hành động (actions), nhưng không rõ liệu có một thanh hành động (`actions-bar`) riêng biệt hay chỉ là các hành động trong cột cố định (`fixed-column.actions`).
- Các hành động hiện tại có thể được kích hoạt thông qua biểu tượng `more_horiz` trong cột hành động của mỗi dòng.

#### **Yêu cầu cải tiến**
- **Thêm thanh hành động (`actions-bar`)**:
  - Thanh hành động là một khu vực giao diện (có thể là một `div` hoặc `mat-toolbar`) nằm ở trên hoặc dưới danh sách, hiển thị các hành động có thể thực hiện trên danh sách sản phẩm.
  - Thanh này sẽ thay đổi nội dung dựa trên trạng thái chọn của các mục trong danh sách.

- **Hành vi của thanh hành động**:
  - **Khi không có mục nào được chọn**:
    - Hiển thị một tập hợp các hành động mặc định (`default-actions`), ví dụ: "Tải lại danh sách", "Thêm sản phẩm mới", hoặc "Lọc nâng cao".
    - Các hành động mặc định này có thể được định nghĩa trong cấu hình của component (`ListConfig`) hoặc được truyền từ component con.
  - **Khi có ít nhất một mục được chọn**:
    - Ẩn các hành động mặc định và hiển thị một tập hợp các hành động dành cho các mục đã chọn (`selected-items-actions`).
    - Các hành động này được nhận dưới dạng **@Input** từ component con (ví dụ: `ProductListComponent`), cho phép component con định nghĩa các hành động tùy chỉnh như "Xóa sản phẩm", "Cập nhật giá", hoặc "Đồng bộ với Shopee".
    - Truyền toàn bộ danh sách các mục đã chọn (`selectedItems`) vào các hành động này để component con có thể xử lý (ví dụ: xóa nhiều sản phẩm cùng lúc).

- **Cách truyền actions từ component con**:
  - Component con sẽ cung cấp một tập hợp các hành động dưới dạng HTML hoặc template (ví dụ: sử dụng `TemplateRef` như trong `ListConfig.fixedColumnTemplate` hoặc `ListConfig.scrollableColumnsTemplate`).
  - Mỗi hành động sẽ nhận được danh sách `selectedItems` để xử lý các tác vụ như cập nhật hàng loạt, xóa, hoặc đồng bộ dữ liệu.

#### **Kịch bản sử dụng**
- Người dùng vào danh sách sản phẩm, không chọn mục nào → Thanh `actions-bar` hiển thị các nút như "Thêm sản phẩm" hoặc "Xuất danh sách".
- Người dùng tick một hoặc nhiều sản phẩm → Thanh `actions-bar` chuyển sang hiển thị các hành động như "Xóa sản phẩm đã chọn", "Cập nhật giá", hoặc "Đồng bộ với TikTok Shop".
- Khi người dùng nhấp vào một hành động (ví dụ: "Xóa"), component con nhận được danh sách `selectedItems` và thực hiện hành động tương ứng.

#### **Lưu ý kỹ thuật**
- Thêm một `@Input` mới trong `ListLayoutComponent` để nhận các hành động từ component con (ví dụ: `actionsTemplate: TemplateRef<any>`).
- Thêm một `@Output` để emit danh sách `selectedItems` khi người dùng kích hoạt một hành động.
- Sử dụng `*ngIf` hoặc các directive tương tự để chuyển đổi giữa `default-actions` và `selected-items-actions` dựa trên độ dài của mảng `selectedItems`.
- Đảm bảo các hành động trong `selected-items-actions` có thể truy cập `selectedItems` để xử lý logic.

---

### **3. Xử lý hành động trên từng dòng sản phẩm**

#### **Mô tả chức năng hiện tại**
- Mỗi dòng sản phẩm có một cột hành động (`fixed-column.actions`) chứa biểu tượng `more_horiz` (sử dụng `mat-icon`).
- Khi nhấp vào `more_horiz`, không rõ liệu có hiển thị menu hành động (`mat-menu`) hay chỉ kích hoạt một hành động cụ thể.

#### **Yêu cầu cải tiến**
- **Thêm @Input cho hành động trên từng dòng**:
  - Cho phép component con truyền vào một tập hợp các hành động tùy chỉnh (`actions`) cho từng dòng sản phẩm, tương tự như cách truyền `actions` cho `selected-items-actions`.
  - Mỗi hành động sẽ nhận được **mục hiện tại** (`item`) của dòng đó để xử lý (ví dụ: chỉnh sửa sản phẩm, xóa sản phẩm, hoặc xem chi tiết).

- **Hiển thị menu hành động**:
  - Khi người dùng nhấp vào biểu tượng `more_horiz` trong cột `fixed-column.actions`, hiển thị một menu (`mat-menu`) chứa các hành động được truyền từ component con.
  - Menu này sẽ render các hành động dưới dạng các mục menu (menu items), mỗi mục tương ứng với một hành động (ví dụ: "Chỉnh sửa", "Xóa", "Xem chi tiết").
  - Mỗi hành động trong menu sẽ nhận được `item` của dòng hiện tại để thực hiện logic tương ứng.

#### **Kịch bản sử dụng**
- Người dùng xem danh sách sản phẩm, thấy cột hành động với biểu tượng `more_horiz` trên mỗi dòng.
- Nhấp vào `more_horiz` → Một menu hiện ra với các tùy chọn như "Chỉnh sửa sản phẩm", "Xóa sản phẩm", hoặc "Đồng bộ với Lazada".
- Khi chọn một hành động (ví dụ: "Chỉnh sửa"), component con nhận được thông tin của sản phẩm (qua `item`) và thực hiện hành động tương ứng.

#### **Lưu ý kỹ thuật**
- Thêm một `@Input` mới trong `ListLayoutComponent` để nhận template hành động cho từng dòng (ví dụ: `rowActionsTemplate: TemplateRef<any>`).
- Sử dụng `mat-menu` để hiển thị danh sách hành động khi nhấp vào `more_horiz`.
- Truyền `item` của dòng hiện tại vào các hành động trong menu thông qua một `@Output` hoặc context của `mat-menu`.
- Đảm bảo menu được định vị chính xác (sử dụng `xPosition` và `yPosition` của `mat-menu`) để tránh che khuất nội dung khác.

---

### **Tóm tắt yêu cầu**
1. **Checkbox (tickbox)**:
   - Checkbox tổng: Trống mặc định, tick tất cả khi nhấp, hiển thị dấu gạch ngang khi chọn một phần, bỏ chọn tất cả khi nhấp lại.
   - Duy trì trạng thái chọn qua các trang bằng mảng `selectedItems`.
2. **Thanh hành động (`actions-bar`)**:
   - Hiển thị `default-actions` khi không có mục nào được chọn.
   - Hiển thị `selected-items-actions` khi có mục được chọn, nhận actions từ component con và truyền `selectedItems`.
3. **Hành động trên từng dòng**:
   - Thêm `@Input` để nhận actions cho từng dòng, hiển thị trong `mat-menu` khi nhấp vào `more_horiz`, truyền `item` vào mỗi hành động.
